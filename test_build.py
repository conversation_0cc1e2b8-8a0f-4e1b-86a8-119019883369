#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包环境和依赖
"""

import sys
import os
import importlib

def test_imports():
    """测试所有必要的导入"""
    print("测试Python模块导入...")
    
    modules_to_test = [
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'flask',
        'flask_cors',
        'openpyxl',
        'sqlite3',
        'threading',
        'webbrowser',
        'socket',
        'datetime',
        'os',
        'sys',
        'time',
        'subprocess',
        'pathlib',
        'shutil'
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
            failed_imports.append(module)
    
    return failed_imports

def test_backend_imports():
    """测试后端模块导入"""
    print("\n测试后端模块导入...")
    
    # 添加backend目录到Python路径
    backend_path = os.path.join(os.path.dirname(__file__), 'backend')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    backend_modules = [
        'backend.app',
        'backend.models',
        'backend.init_db'
    ]
    
    failed_imports = []
    
    for module in backend_modules:
        try:
            importlib.import_module(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
            failed_imports.append(module)
    
    return failed_imports

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        'gui_launcher.py',
        'console_launcher.py',
        'build_exe.py',
        'build.bat',
        'requirements.txt',
        'run.py',
        'backend/app.py',
        'backend/models.py',
        'backend/init_db.py',
        'frontend/index.html',
        'frontend/admin.html'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
    
    return missing_files

def test_directories():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = [
        'backend',
        'frontend',
        'frontend/js',
        'frontend/css'
    ]
    
    missing_dirs = []
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✓ {dir_path}/")
        else:
            print(f"✗ {dir_path}/")
            missing_dirs.append(dir_path)
    
    return missing_dirs

def test_flask_app():
    """测试Flask应用创建"""
    print("\n测试Flask应用创建...")
    
    try:
        # 添加backend目录到Python路径
        backend_path = os.path.join(os.path.dirname(__file__), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        
        from backend.app import create_app
        app = create_app()
        print("✓ Flask应用创建成功")
        print(f"✓ 应用名称: {app.name}")
        return True
    except Exception as e:
        print(f"✗ Flask应用创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("电子元器件管理系统 - 打包环境测试")
    print("=" * 60)
    
    # 测试Python模块导入
    failed_imports = test_imports()
    
    # 测试后端模块导入
    failed_backend = test_backend_imports()
    
    # 测试文件结构
    missing_files = test_file_structure()
    
    # 测试目录结构
    missing_dirs = test_directories()
    
    # 测试Flask应用
    flask_ok = test_flask_app()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_ok = True
    
    if failed_imports:
        print(f"✗ 缺失Python模块: {', '.join(failed_imports)}")
        all_ok = False
    else:
        print("✓ 所有Python模块导入正常")
    
    if failed_backend:
        print(f"✗ 缺失后端模块: {', '.join(failed_backend)}")
        all_ok = False
    else:
        print("✓ 所有后端模块导入正常")
    
    if missing_files:
        print(f"✗ 缺失文件: {', '.join(missing_files)}")
        all_ok = False
    else:
        print("✓ 所有必需文件存在")
    
    if missing_dirs:
        print(f"✗ 缺失目录: {', '.join(missing_dirs)}")
        all_ok = False
    else:
        print("✓ 所有必需目录存在")
    
    if not flask_ok:
        print("✗ Flask应用创建失败")
        all_ok = False
    else:
        print("✓ Flask应用创建正常")
    
    print("\n" + "=" * 60)
    if all_ok:
        print("🎉 所有测试通过！可以进行打包")
        print("运行 build.bat 或 python build_exe.py 开始打包")
    else:
        print("❌ 存在问题，请先解决上述问题再进行打包")
    print("=" * 60)
    
    return all_ok

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            print("\n按回车键退出...")
            input()
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        print("按回车键退出...")
        input()
