#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示脚本 - 展示GUI启动器功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import time
import threading

class DemoGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("电子元器件管理系统 - 演示版")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        self.create_demo_widgets()
        
    def create_demo_widgets(self):
        """创建演示界面"""
        # 标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(
            title_frame, 
            text="🎉 电子元器件管理系统", 
            font=("Arial", 18, "bold")
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            title_frame, 
            text="PyInstaller 打包演示", 
            font=("Arial", 12),
            foreground="blue"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 功能展示区域
        features_frame = ttk.LabelFrame(self.root, text="✨ 新增功能特性", padding=15)
        features_frame.pack(pady=20, padx=20, fill="x")
        
        features = [
            "🖥️ 用户友好的GUI界面",
            "🚀 一键启动/停止Web服务",
            "🌐 自动打开浏览器",
            "📊 实时服务状态显示",
            "📝 详细的操作日志",
            "❌ 隐藏控制台窗口",
            "📦 独立可执行文件",
            "🔧 无需Python环境"
        ]
        
        for i, feature in enumerate(features):
            label = ttk.Label(features_frame, text=feature, font=("Arial", 10))
            label.grid(row=i//2, column=i%2, sticky="w", padx=10, pady=5)
        
        # 演示按钮区域
        demo_frame = ttk.LabelFrame(self.root, text="🎮 功能演示", padding=15)
        demo_frame.pack(pady=20, padx=20, fill="x")
        
        # 按钮行1
        button_frame1 = ttk.Frame(demo_frame)
        button_frame1.pack(pady=5)
        
        ttk.Button(
            button_frame1, 
            text="🚀 启动GUI启动器", 
            command=self.launch_gui,
            width=20
        ).pack(side="left", padx=5)
        
        ttk.Button(
            button_frame1, 
            text="💻 启动控制台版本", 
            command=self.launch_console,
            width=20
        ).pack(side="left", padx=5)
        
        # 按钮行2
        button_frame2 = ttk.Frame(demo_frame)
        button_frame2.pack(pady=5)
        
        ttk.Button(
            button_frame2, 
            text="🔧 测试打包环境", 
            command=self.test_environment,
            width=20
        ).pack(side="left", padx=5)
        
        ttk.Button(
            button_frame2, 
            text="📦 开始打包", 
            command=self.start_build,
            width=20
        ).pack(side="left", padx=5)
        
        # 说明区域
        info_frame = ttk.LabelFrame(self.root, text="📋 使用说明", padding=15)
        info_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, font=("Arial", 9))
        scrollbar = ttk.Scrollbar(info_frame, orient="vertical", command=info_text.yview)
        info_text.configure(yscrollcommand=scrollbar.set)
        
        info_content = """
📖 快速开始指南：

1. 🔧 点击"测试打包环境" - 检查所有依赖是否正常
2. 🚀 点击"启动GUI启动器" - 体验用户友好的图形界面
3. 💻 点击"启动控制台版本" - 查看备用的命令行版本
4. 📦 点击"开始打包" - 将程序打包成独立的exe文件

📦 打包后的使用：
- 双击 "电子元器件管理系统.exe"
- 在GUI界面点击"启动服务"
- 程序自动打开浏览器
- 开始使用Web界面管理元器件

🎯 目标用户体验：
- 完全隐藏技术细节
- 一键启动，自动打开浏览器
- 无需Python环境，可在任何Windows电脑运行
- 用户友好的图形界面操作
        """
        
        info_text.insert(tk.END, info_content.strip())
        info_text.config(state=tk.DISABLED)
        
        info_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 底部按钮
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(pady=10)
        
        ttk.Button(
            bottom_frame, 
            text="📚 查看文档", 
            command=self.open_docs,
            width=15
        ).pack(side="left", padx=5)
        
        ttk.Button(
            bottom_frame, 
            text="🌐 访问项目", 
            command=self.open_project,
            width=15
        ).pack(side="left", padx=5)
        
        ttk.Button(
            bottom_frame, 
            text="❌ 退出演示", 
            command=self.root.quit,
            width=15
        ).pack(side="left", padx=5)
        
    def launch_gui(self):
        """启动GUI启动器"""
        try:
            import subprocess
            subprocess.Popen(["python", "gui_launcher.py"])
            messagebox.showinfo("成功", "GUI启动器已启动！\n请查看新打开的窗口。")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败：{e}")
            
    def launch_console(self):
        """启动控制台版本"""
        try:
            import subprocess
            subprocess.Popen(["python", "console_launcher.py"], creationflags=subprocess.CREATE_NEW_CONSOLE)
            messagebox.showinfo("成功", "控制台版本已启动！\n请查看新打开的控制台窗口。")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败：{e}")
            
    def test_environment(self):
        """测试打包环境"""
        try:
            import subprocess
            subprocess.Popen(["python", "test_build.py"], creationflags=subprocess.CREATE_NEW_CONSOLE)
            messagebox.showinfo("测试启动", "环境测试已启动！\n请查看控制台窗口的测试结果。")
        except Exception as e:
            messagebox.showerror("错误", f"测试启动失败：{e}")
            
    def start_build(self):
        """开始打包"""
        result = messagebox.askyesno(
            "确认打包", 
            "确定要开始打包吗？\n\n这将：\n1. 安装PyInstaller\n2. 创建配置文件\n3. 生成可执行文件\n\n过程可能需要几分钟时间。"
        )
        if result:
            try:
                import subprocess
                subprocess.Popen(["build.bat"], shell=True)
                messagebox.showinfo("打包启动", "打包过程已启动！\n请查看控制台窗口的进度。")
            except Exception as e:
                messagebox.showerror("错误", f"打包启动失败：{e}")
                
    def open_docs(self):
        """打开文档"""
        docs = [
            "README.md",
            "打包说明.md", 
            "快速开始.md"
        ]
        
        doc_window = tk.Toplevel(self.root)
        doc_window.title("📚 项目文档")
        doc_window.geometry("300x200")
        
        ttk.Label(doc_window, text="选择要查看的文档：", font=("Arial", 12)).pack(pady=10)
        
        for doc in docs:
            ttk.Button(
                doc_window, 
                text=doc, 
                command=lambda d=doc: self._open_file(d),
                width=30
            ).pack(pady=5)
            
    def _open_file(self, filename):
        """打开文件"""
        try:
            import os
            os.startfile(filename)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件：{e}")
            
    def open_project(self):
        """访问项目"""
        try:
            webbrowser.open("http://localhost:5000")
        except Exception as e:
            messagebox.showinfo("提示", "请先启动Web服务后再访问项目。")
            
    def run(self):
        """运行演示"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        demo = DemoGUI()
        demo.run()
    except Exception as e:
        print(f"演示启动失败：{e}")

if __name__ == '__main__':
    main()
