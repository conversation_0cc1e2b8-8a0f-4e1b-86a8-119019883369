# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件和目录
datas = [
    ('frontend', 'frontend'),
    ('data', 'data'),
    ('exports', 'exports'),
    ('backend', 'backend'),
]

# 隐藏导入的模块
hiddenimports = [
    'backend.app',
    'backend.models',
    'backend.init_db',
    'app',  # 添加直接导入的app模块
    'models',  # 添加直接导入的models模块
    'flask',
    'flask_cors',
    'openpyxl',
    'openpyxl.styles',
    'openpyxl.utils',
    'sqlite3',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'webbrowser',
    'threading',
    'socket',
    'datetime',
    'json',
    'os',
    'sys',
]

a = Analysis(
    ['gui_launcher.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='电气元器件管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
