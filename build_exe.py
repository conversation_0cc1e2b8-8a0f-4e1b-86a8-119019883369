#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将电气元器件管理系统打包成独立的可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"删除文件: {spec_file}")
        os.remove(spec_file)

def create_pyinstaller_spec():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件和目录
datas = [
    ('frontend', 'frontend'),
    ('data', 'data'),
    ('exports', 'exports'),
    ('backend', 'backend'),
]

# 隐藏导入的模块
hiddenimports = [
    'backend.app',
    'backend.models',
    'backend.init_db',
    'app',  # 添加直接导入的app模块
    'models',  # 添加直接导入的models模块
    'flask',
    'flask_cors',
    'openpyxl',
    'openpyxl.styles',
    'openpyxl.utils',
    'sqlite3',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'webbrowser',
    'threading',
    'socket',
    'datetime',
    'json',
    'os',
    'sys',
]

a = Analysis(
    ['gui_launcher.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='电气元器件管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('component_manager.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建 component_manager.spec 配置文件")

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller 已安装")
        return True
    except ImportError:
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"PyInstaller 安装失败: {e}")
            return False

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', 'component_manager.spec']
        subprocess.check_call(cmd)
        print("构建完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False

def create_startup_script():
    """创建简单的启动脚本"""
    startup_script = '''@echo off
echo 正在启动电气元器件管理系统...
echo.
"电气元器件管理系统.exe"
pause
'''
    
    dist_dir = Path('dist')
    if dist_dir.exists():
        with open(dist_dir / '启动程序.bat', 'w', encoding='gbk') as f:
            f.write(startup_script)
        print("已创建启动脚本: dist/启动程序.bat")

def create_readme():
    """创建使用说明文件"""
    readme_content = '''# 电气元器件管理系统 - 独立版本

## 使用说明

1. 双击 "电气元器件管理系统.exe" 启动程序
2. 或者双击 "启动程序.bat" 启动（会显示启动信息）
3. 在弹出的GUI界面中点击"启动服务"按钮
4. 程序会自动打开浏览器访问管理界面
5. 使用完毕后点击"停止服务"按钮或直接关闭GUI窗口

## 功能特性

- 三级分类管理（制造商 → 产品组 → 子产品组）
- 元器件资料录入（型号、描述、备注）
- 多种搜索方式：关键词搜索、分类浏览
- 数据选择与导出（Excel格式）
- 本地运行，无需外网连接
- 无需安装Python环境

## 数据存储

- 数据库文件：data/components.db
- 导出文件：exports/ 目录
- 所有数据都保存在本地，可以备份整个程序目录

## 注意事项

- 首次运行可能需要几秒钟启动时间
- 请确保5000端口未被其他程序占用
- 关闭程序前建议先停止服务
- 如需备份数据，请复制整个程序目录

## 技术支持

如有问题，请检查：
1. 防火墙是否阻止程序运行
2. 5000端口是否被占用
3. 程序目录是否有写入权限

版本：1.0
构建时间：''' + str(Path(__file__).stat().st_mtime) + '''
'''
    
    dist_dir = Path('dist')
    if dist_dir.exists():
        with open(dist_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("已创建使用说明: dist/使用说明.txt")

def main():
    """主函数"""
    print("=" * 50)
    print("电气元器件管理系统 - PyInstaller 打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('gui_launcher.py'):
        print("错误：请在项目根目录运行此脚本")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    # 创建spec文件
    create_pyinstaller_spec()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建附加文件
    create_startup_script()
    create_readme()
    
    print("\n" + "=" * 50)
    print("打包完成！")
    print("可执行文件位置: dist/电气元器件管理系统.exe")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        input("按回车键退出...")
