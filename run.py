#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电子元器件管理系统启动脚本
"""

import os
import sys
import webbrowser
import time
from threading import Timer

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# 修改当前工作目录到backend
os.chdir(os.path.join(os.path.dirname(__file__), 'backend'))
from app import create_app

def open_browser():
    """延迟打开浏览器"""
    webbrowser.open('http://localhost:5000')

if __name__ == '__main__':
    # 回到项目根目录
    os.chdir(os.path.dirname(__file__))

    # 创建必要的目录
    os.makedirs('data', exist_ok=True)
    os.makedirs('exports', exist_ok=True)
    
    # 创建Flask应用
    app = create_app()
    
    print("=" * 50)
    print("电子元器件管理系统")
    print("=" * 50)
    print("正在启动服务器...")
    print("服务器地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 延迟1秒后自动打开浏览器
    Timer(1.0, open_browser).start()
    
    # 启动Flask应用
    try:
        app.run(host='localhost', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
