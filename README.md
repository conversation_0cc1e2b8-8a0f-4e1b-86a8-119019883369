# 电子元器件管理系统

一个基于Python Flask + SQLite + Vue.js的本地电子元器件资料管理软件。

## 功能特性

- 三级分类管理（制造商 → 产品组 → 子产品组）
- 元器件资料录入（型号、描述、备注）
- 多种搜索方式：关键词搜索、分类浏览
- 数据选择与导出（Excel格式）
- 本地运行，无需外网连接

## 技术栈

- 后端：Python Flask + SQLite
- 前端：Vue.js + Element UI
- 数据导出：openpyxl

## 项目结构

```
dian<PERSON>jian/
├── backend/                 # 后端代码
│   ├── app.py              # Flask应用主文件
│   ├── models.py           # 数据库模型
│   ├── api/                # API路由
│   └── database/           # 数据库相关
├── frontend/               # 前端代码
│   ├── index.html          # 主页面
│   ├── js/                 # JavaScript文件
│   ├── css/                # 样式文件
│   └── components/         # Vue组件
├── data/                   # 数据文件
│   └── components.db       # SQLite数据库
├── exports/                # 导出文件目录
├── requirements.txt        # Python依赖
└── run.py                  # 启动脚本
```

## 安装与运行

### 开发环境运行

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 初始化数据库：
```bash
python backend/init_db.py
```

3. 启动应用：
```bash
python run.py
```

4. 打开浏览器访问：http://localhost:5000

### 打包为独立可执行文件

1. **一键打包（推荐）**：
```bash
# 双击运行
build.bat
```

2. **手动打包**：
```bash
# 测试环境
python test_build.py

# 开始打包
python build_exe.py
```

3. **使用打包后的程序**：
   - 双击 `dist/电子元器件管理系统.exe`
   - 在GUI界面点击"启动服务"
   - 程序自动打开浏览器
   - 无需Python环境，可在任何Windows电脑运行

## 新增功能

### 🎯 用户友好的PC版本
- **GUI启动器** - 图形界面，一键启动/停止
- **隐藏控制台** - 无黑色窗口，用户体验更好
- **自动浏览器** - 启动后自动打开网页
- **状态显示** - 实时显示服务运行状态
- **操作日志** - 详细的运行日志记录

### 📦 一键打包系统
- **自动化打包** - 一个批处理文件完成所有操作
- **环境检测** - 自动检测依赖和环境
- **独立运行** - 打包后无需Python环境
- **完整功能** - 包含所有Web功能

## 使用说明

### 数据录入
- 选择三级分类（制造商 → 产品组 → 子产品组）
- 填写元器件信息（型号、描述、备注）
- 支持批量录入

### 数据搜索与导出
- 方式一：关键词搜索，勾选需要的元器件
- 方式二：按分类浏览，勾选需要的元器件
- 右侧显示已选择的元器件列表
- 点击导出按钮生成Excel文件

## 文件说明

### 核心文件
- `run.py` - 原始命令行启动脚本
- `gui_launcher.py` - **新增** GUI图形界面启动器
- `console_launcher.py` - **新增** 控制台启动器（备用）

### 打包相关
- `build_exe.py` - **新增** PyInstaller自动打包脚本
- `build.bat` - **新增** 一键打包批处理文件
- `test_build.py` - **新增** 环境测试脚本

### 说明文档
- `打包说明.md` - **新增** 详细的打包说明
- `快速开始.md` - **新增** 快速使用指南
- `图标说明.md` - **新增** 自定义图标说明

## 快速开始

### 对于开发者
1. 克隆项目
2. 运行 `python test_build.py` 测试环境
3. 运行 `build.bat` 一键打包
4. 在 `dist` 目录获得可执行文件

### 对于最终用户
1. 获得 `电子元器件管理系统.exe` 文件
2. 双击运行
3. 点击"启动服务"按钮
4. 开始使用（浏览器自动打开）

## 技术特点

- ✅ **零依赖部署** - 打包后无需Python环境
- ✅ **用户友好** - GUI界面，隐藏技术细节
- ✅ **一键操作** - 启动、停止都是一键完成
- ✅ **自动化** - 自动打开浏览器，自动创建目录
- ✅ **完整功能** - 包含原有的所有Web功能
- ✅ **本地运行** - 无需网络，数据安全
