@echo off
chcp 65001 >nul
echo ================================================
echo 电子元器件管理系统 - 自动打包工具
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python
    pause
    exit /b 1
)
echo ✓ Python环境检查通过

echo.
echo 检查项目文件...
if not exist "gui_launcher.py" (
    echo 错误：未找到gui_launcher.py文件
    pause
    exit /b 1
)
if not exist "backend\app.py" (
    echo 错误：未找到backend\app.py文件
    pause
    exit /b 1
)
echo ✓ 项目文件检查通过

echo.
echo 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 警告：依赖包安装可能有问题，继续尝试打包...
)

echo.
echo 开始打包...
python build_exe.py
if errorlevel 1 (
    echo 打包失败！
    pause
    exit /b 1
)

echo.
echo ================================================
echo 打包完成！
echo ================================================
echo 可执行文件位置: dist\电子元器件管理系统.exe
echo 启动脚本位置: dist\启动程序.bat
echo 使用说明位置: dist\使用说明.txt
echo ================================================
echo.

echo 是否要打开dist目录？(Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer dist
)

pause
