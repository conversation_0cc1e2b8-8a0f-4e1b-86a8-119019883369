#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电子元器件管理系统 - 控制台启动器
提供命令行界面来启动Web服务（备用方案）
"""

import os
import sys
import webbrowser
import time
import signal
import threading
from datetime import datetime

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

class ConsoleManager:
    def __init__(self):
        self.server_thread = None
        self.is_running = False
        self.app = None
        
    def print_banner(self):
        """打印程序横幅"""
        print("=" * 60)
        print("           电子元器件管理系统")
        print("=" * 60)
        print("版本: 1.0")
        print("端口: 5000")
        print("访问地址: http://localhost:5000")
        print("=" * 60)
        print()
        
    def start_server(self):
        """启动服务器"""
        try:
            print("正在初始化...")
            
            # 创建必要的目录
            os.makedirs('data', exist_ok=True)
            os.makedirs('exports', exist_ok=True)
            print("✓ 目录检查完成")
            
            # 导入Flask应用
            from backend.app import create_app
            self.app = create_app()
            print("✓ 应用初始化完成")
            
            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            if self.is_running:
                print("✓ 服务器启动成功")
                print(f"✓ 服务运行在: http://localhost:5000")
                
                # 自动打开浏览器
                print("正在打开浏览器...")
                try:
                    webbrowser.open('http://localhost:5000')
                    print("✓ 浏览器已打开")
                except Exception as e:
                    print(f"⚠ 自动打开浏览器失败: {e}")
                    print("请手动访问: http://localhost:5000")
                
                return True
            else:
                print("✗ 服务器启动失败")
                return False
                
        except Exception as e:
            print(f"✗ 启动失败: {e}")
            return False
            
    def _run_server(self):
        """在后台线程中运行服务器"""
        try:
            self.is_running = True
            # 启动Flask应用（禁用调试模式和重载器）
            self.app.run(host='localhost', port=5000, debug=False, use_reloader=False)
        except Exception as e:
            print(f"服务器运行错误: {e}")
            self.is_running = False
            
    def stop_server(self):
        """停止服务器"""
        print("\n正在停止服务器...")
        self.is_running = False
        print("✓ 服务器已停止")
        
    def wait_for_exit(self):
        """等待用户退出"""
        print("\n" + "=" * 60)
        print("服务器正在运行中...")
        print("按 Ctrl+C 停止服务器并退出程序")
        print("或直接关闭此窗口")
        print("=" * 60)
        
        try:
            # 设置信号处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 保持程序运行
            while self.is_running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            self._signal_handler(signal.SIGINT, None)
            
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n收到退出信号...")
        self.stop_server()
        sys.exit(0)
        
    def run(self):
        """运行控制台管理器"""
        self.print_banner()
        
        if self.start_server():
            self.wait_for_exit()
        else:
            print("\n程序启动失败，按回车键退出...")
            input()

def main():
    """主函数"""
    try:
        manager = ConsoleManager()
        manager.run()
    except Exception as e:
        print(f"程序运行错误: {e}")
        print("按回车键退出...")
        input()

if __name__ == '__main__':
    main()
