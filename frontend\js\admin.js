new Vue({
    el: '#app',
    data() {
        return {
            // 认证相关
            isAuthenticated: false,
            loginPassword: '',
            loginLoading: false,
            
            // 标签页
            activeTab: 'manufacturers',
            
            // 制造商管理
            manufacturers: [],
            manufacturerLoading: false,
            manufacturerSearch: '',
            manufacturerPage: 1,
            manufacturerPageSize: 10,
            manufacturerTotal: 0,
            selectedManufacturers: [], // 批量选择的制造商
            
            // 产品组管理
            productGroups: [],
            productGroupLoading: false,
            productGroupSearch: '',
            productGroupPage: 1,
            productGroupPageSize: 10,
            productGroupTotal: 0,
            productGroupManufacturerFilter: '',
            selectedProductGroups: [], // 批量选择的产品组
            
            // 子产品组管理
            subProductGroups: [],
            subProductGroupLoading: false,
            subProductGroupSearch: '',
            subProductGroupPage: 1,
            subProductGroupPageSize: 10,
            subProductGroupTotal: 0,
            subProductGroupManufacturerFilter: '',
            subProductGroupProductGroupFilter: '',
            selectedSubProductGroups: [], // 批量选择的子产品组
            
            // 元器件管理
            components: [],
            componentLoading: false,
            componentSearch: '',
            componentPage: 1,
            componentPageSize: 10,
            componentTotal: 0,
            componentManufacturerFilter: '',
            componentProductGroupFilter: '',
            componentSubProductGroupFilter: '',
            selectedComponents: [], // 批量选择的元器件
            
            // 所有数据（用于下拉选择）
            allManufacturers: [],
            allProductGroups: [],
            allSubProductGroups: [],
            allProductGroupNames: [], // 所有唯一的产品组名称
            
            // 编辑对话框
            editDialogVisible: false,
            editDialogTitle: '',
            editDialogType: '',
            editForm: {},
            editFormRules: {},
            
            // 添加对话框
            addDialogVisible: false,
            addDialogTitle: '',
            addDialogType: '',
            addForm: {},
            addFormRules: {}
        }
    },
    
    computed: {
        // 根据制造商筛选产品组
        filteredProductGroups() {
            if (!this.subProductGroupManufacturerFilter) {
                return this.allProductGroups;
            }
            return this.allProductGroups.filter(pg => pg.manufacturer_id === this.subProductGroupManufacturerFilter);
        },
        
        // 根据制造商筛选产品组（元器件用）
        filteredProductGroupsForComponent() {
            if (!this.componentManufacturerFilter) {
                return this.allProductGroups;
            }
            return this.allProductGroups.filter(pg => pg.manufacturer_id === this.componentManufacturerFilter);
        },
        
        // 根据产品组筛选子产品组（元器件用）
        filteredSubProductGroupsForComponent() {
            if (!this.componentProductGroupFilter) {
                return this.allSubProductGroups;
            }
            return this.allSubProductGroups.filter(spg => spg.product_group_id === this.componentProductGroupFilter);
        },

        // 编辑对话框中的筛选
        editFilteredProductGroups() {
            if (!this.editForm.manufacturer_id) {
                return this.allProductGroups;
            }
            return this.allProductGroups.filter(pg => pg.manufacturer_id === this.editForm.manufacturer_id);
        },

        editFilteredSubProductGroups() {
            if (!this.editForm.product_group_id) {
                return this.allSubProductGroups;
            }
            return this.allSubProductGroups.filter(spg => spg.product_group_id === this.editForm.product_group_id);
        },

        // 添加对话框中的筛选
        addFilteredProductGroups() {
            if (!this.addForm.manufacturer_id) {
                return this.allProductGroups;
            }
            return this.allProductGroups.filter(pg => pg.manufacturer_id === this.addForm.manufacturer_id);
        },

        addFilteredSubProductGroups() {
            if (!this.addForm.product_group_id) {
                return this.allSubProductGroups;
            }
            return this.allSubProductGroups.filter(spg => spg.product_group_id === this.addForm.product_group_id);
        }
    },
    
    mounted() {
        this.loadAllData();
    },
    
    methods: {
        // 登录
        async login() {
            if (!this.loginPassword) {
                this.$message.error('请输入密码');
                return;
            }
            
            this.loginLoading = true;
            try {
                const response = await fetch('/api/admin/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password: this.loginPassword })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.isAuthenticated = true;
                    this.loginPassword = '';
                    this.$message.success('登录成功');
                    this.loadManufacturers();
                    this.loadAllData(); // 加载所有基础数据，确保下拉框有数据
                } else {
                    this.$message.error(result.message || '登录失败');
                }
            } catch (error) {
                this.$message.error('登录请求失败');
                console.error('Login error:', error);
            } finally {
                this.loginLoading = false;
            }
        },
        
        // 退出登录
        logout() {
            this.isAuthenticated = false;
            this.loginPassword = '';
            this.$message.success('已退出登录');
        },

        // 返回主页
        goToMainPage() {
            window.location.href = '/';
        },
        
        // 标签页切换
        handleTabClick(tab) {
            switch (tab.name) {
                case 'manufacturers':
                    this.loadManufacturers();
                    break;
                case 'product_groups':
                    this.loadProductGroups();
                    break;
                case 'sub_product_groups':
                    this.loadSubProductGroups();
                    break;
                case 'components':
                    this.loadComponents();
                    break;
            }
        },
        
        // 加载所有基础数据
        async loadAllData() {
            try {
                // 加载所有制造商
                const mfgResponse = await fetch('/api/manufacturers');
                this.allManufacturers = await mfgResponse.json();

                // 加载所有产品组
                const pgResponse = await fetch('/api/admin/product_groups?page_size=1000');
                const pgResult = await pgResponse.json();
                this.allProductGroups = pgResult.data || [];

                // 加载所有产品组名称
                const pgNamesResponse = await fetch('/api/admin/product_group_names');
                this.allProductGroupNames = await pgNamesResponse.json();

                // 加载所有子产品组
                const spgResponse = await fetch('/api/admin/sub_product_groups?page_size=1000');
                const spgResult = await spgResponse.json();
                this.allSubProductGroups = spgResult.data || [];
            } catch (error) {
                console.error('Load all data error:', error);
            }
        },
        
        // ==================== 制造商管理 ====================
        
        async loadManufacturers() {
            this.manufacturerLoading = true;
            try {
                const params = new URLSearchParams({
                    page: this.manufacturerPage,
                    page_size: this.manufacturerPageSize
                });
                
                if (this.manufacturerSearch) {
                    params.append('search', this.manufacturerSearch);
                }
                
                const response = await fetch(`/api/admin/manufacturers?${params}`);
                const result = await response.json();
                
                this.manufacturers = result.data || [];
                this.manufacturerTotal = result.total || 0;
            } catch (error) {
                this.$message.error('加载制造商数据失败');
                console.error('Load manufacturers error:', error);
            } finally {
                this.manufacturerLoading = false;
            }
        },
        
        handleManufacturerSizeChange(val) {
            this.manufacturerPageSize = val;
            this.manufacturerPage = 1;
            this.loadManufacturers();
        },
        
        handleManufacturerCurrentChange(val) {
            this.manufacturerPage = val;
            this.loadManufacturers();
        },
        
        showAddManufacturerDialog() {
            this.addDialogVisible = true;
            this.addDialogTitle = '添加制造商';
            this.addDialogType = 'manufacturer';
            this.addForm = {
                name: '',
                description: ''
            };
            this.addFormRules = {
                name: [{ required: true, message: '请输入制造商名称', trigger: 'blur' }]
            };
        },
        
        editManufacturer(row) {
            this.editDialogVisible = true;
            this.editDialogTitle = '编辑制造商';
            this.editDialogType = 'manufacturer';
            this.editForm = { ...row };
            this.editFormRules = {
                name: [{ required: true, message: '请输入制造商名称', trigger: 'blur' }]
            };
        },
        
        async deleteManufacturer(row) {
            try {
                await this.$confirm(`确定要删除制造商"${row.name}"吗？`, '确认删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const response = await fetch(`/api/admin/manufacturers/${row.id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.$message.success('删除成功');
                    this.loadManufacturers();
                    this.loadAllData(); // 重新加载基础数据
                } else {
                    this.$message.error(result.error || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除请求失败');
                    console.error('Delete manufacturer error:', error);
                }
            }
        },
        
        // ==================== 产品组管理 ====================
        
        async loadProductGroups() {
            this.productGroupLoading = true;
            try {
                const params = new URLSearchParams({
                    page: this.productGroupPage,
                    page_size: this.productGroupPageSize
                });
                
                if (this.productGroupSearch) {
                    params.append('search', this.productGroupSearch);
                }
                
                if (this.productGroupManufacturerFilter) {
                    params.append('manufacturer_id', this.productGroupManufacturerFilter);
                }
                
                const response = await fetch(`/api/admin/product_groups?${params}`);
                const result = await response.json();
                
                this.productGroups = result.data || [];
                this.productGroupTotal = result.total || 0;
            } catch (error) {
                this.$message.error('加载产品组数据失败');
                console.error('Load product groups error:', error);
            } finally {
                this.productGroupLoading = false;
            }
        },
        
        handleProductGroupSizeChange(val) {
            this.productGroupPageSize = val;
            this.productGroupPage = 1;
            this.loadProductGroups();
        },
        
        handleProductGroupCurrentChange(val) {
            this.productGroupPage = val;
            this.loadProductGroups();
        },
        
        showAddProductGroupDialog() {
            this.addDialogVisible = true;
            this.addDialogTitle = '添加产品组';
            this.addDialogType = 'product_group';
            this.addForm = {
                manufacturer_id: '',
                name: '',
                description: ''
            };
            this.addFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                name: [{ required: true, message: '请选择或输入产品组名称', trigger: 'change' }]
            };
        },
        
        editProductGroup(row) {
            this.editDialogVisible = true;
            this.editDialogTitle = '编辑产品组';
            this.editDialogType = 'product_group';
            this.editForm = { ...row };
            this.editFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                name: [{ required: true, message: '请选择或输入产品组名称', trigger: 'change' }]
            };
        },
        
        async deleteProductGroup(row) {
            try {
                await this.$confirm(`确定要删除产品组"${row.name}"吗？`, '确认删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const response = await fetch(`/api/admin/product_groups/${row.id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.$message.success('删除成功');
                    this.loadProductGroups();
                    this.loadAllData(); // 重新加载基础数据
                } else {
                    this.$message.error(result.error || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除请求失败');
                    console.error('Delete product group error:', error);
                }
            }
        },

        // ==================== 子产品组管理 ====================

        async loadSubProductGroups() {
            this.subProductGroupLoading = true;
            try {
                const params = new URLSearchParams({
                    page: this.subProductGroupPage,
                    page_size: this.subProductGroupPageSize
                });

                if (this.subProductGroupSearch) {
                    params.append('search', this.subProductGroupSearch);
                }

                if (this.subProductGroupManufacturerFilter) {
                    params.append('manufacturer_id', this.subProductGroupManufacturerFilter);
                }

                if (this.subProductGroupProductGroupFilter) {
                    params.append('product_group_id', this.subProductGroupProductGroupFilter);
                }

                const response = await fetch(`/api/admin/sub_product_groups?${params}`);
                const result = await response.json();

                this.subProductGroups = result.data || [];
                this.subProductGroupTotal = result.total || 0;
            } catch (error) {
                this.$message.error('加载子产品组数据失败');
                console.error('Load sub product groups error:', error);
            } finally {
                this.subProductGroupLoading = false;
            }
        },

        handleSubProductGroupSizeChange(val) {
            this.subProductGroupPageSize = val;
            this.subProductGroupPage = 1;
            this.loadSubProductGroups();
        },

        handleSubProductGroupCurrentChange(val) {
            this.subProductGroupPage = val;
            this.loadSubProductGroups();
        },

        onSubProductGroupManufacturerChange() {
            this.subProductGroupProductGroupFilter = '';
            this.loadSubProductGroups();
        },

        showAddSubProductGroupDialog() {
            this.addDialogVisible = true;
            this.addDialogTitle = '添加子产品组';
            this.addDialogType = 'sub_product_group';
            this.addForm = {
                manufacturer_id: '',
                product_group_id: '',
                name: '',
                description: ''
            };
            this.addFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                product_group_id: [{ required: true, message: '请选择产品组', trigger: 'change' }],
                name: [{ required: true, message: '请输入子产品组名称', trigger: 'blur' }]
            };
        },

        editSubProductGroup(row) {
            this.editDialogVisible = true;
            this.editDialogTitle = '编辑子产品组';
            this.editDialogType = 'sub_product_group';
            this.editForm = { ...row };
            this.editFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                product_group_id: [{ required: true, message: '请选择产品组', trigger: 'change' }],
                name: [{ required: true, message: '请输入子产品组名称', trigger: 'blur' }]
            };
        },

        async deleteSubProductGroup(row) {
            try {
                await this.$confirm(`确定要删除子产品组"${row.name}"吗？`, '确认删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const response = await fetch(`/api/admin/sub_product_groups/${row.id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    this.$message.success('删除成功');
                    this.loadSubProductGroups();
                    this.loadAllData(); // 重新加载基础数据
                } else {
                    this.$message.error(result.error || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除请求失败');
                    console.error('Delete sub product group error:', error);
                }
            }
        },

        // ==================== 元器件管理 ====================

        async loadComponents() {
            this.componentLoading = true;
            try {
                const params = new URLSearchParams({
                    page: this.componentPage,
                    page_size: this.componentPageSize
                });

                if (this.componentSearch) {
                    params.append('search', this.componentSearch);
                }

                if (this.componentManufacturerFilter) {
                    params.append('manufacturer_id', this.componentManufacturerFilter);
                }

                if (this.componentProductGroupFilter) {
                    params.append('product_group_id', this.componentProductGroupFilter);
                }

                if (this.componentSubProductGroupFilter) {
                    params.append('sub_product_group_id', this.componentSubProductGroupFilter);
                }

                const response = await fetch(`/api/admin/components?${params}`);
                const result = await response.json();

                this.components = result.data || [];
                this.componentTotal = result.total || 0;
            } catch (error) {
                this.$message.error('加载元器件数据失败');
                console.error('Load components error:', error);
            } finally {
                this.componentLoading = false;
            }
        },

        handleComponentSizeChange(val) {
            this.componentPageSize = val;
            this.componentPage = 1;
            this.loadComponents();
        },

        handleComponentCurrentChange(val) {
            this.componentPage = val;
            this.loadComponents();
        },

        onComponentManufacturerChange() {
            this.componentProductGroupFilter = '';
            this.componentSubProductGroupFilter = '';
            this.loadComponents();
        },

        onComponentProductGroupChange() {
            this.componentSubProductGroupFilter = '';
            this.loadComponents();
        },

        showAddComponentDialog() {
            this.addDialogVisible = true;
            this.addDialogTitle = '添加元器件';
            this.addDialogType = 'component';
            this.addForm = {
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: '',
                model: '',
                description: '',
                notes: ''
            };
            this.addFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                product_group_id: [{ required: true, message: '请选择产品组', trigger: 'change' }],
                sub_product_group_id: [{ required: true, message: '请选择子产品组', trigger: 'change' }],
                model: [{ required: true, message: '请输入型号', trigger: 'blur' }]
            };
        },

        editComponent(row) {
            this.editDialogVisible = true;
            this.editDialogTitle = '编辑元器件';
            this.editDialogType = 'component';
            this.editForm = { ...row };
            this.editFormRules = {
                manufacturer_id: [{ required: true, message: '请选择制造商', trigger: 'change' }],
                product_group_id: [{ required: true, message: '请选择产品组', trigger: 'change' }],
                sub_product_group_id: [{ required: true, message: '请选择子产品组', trigger: 'change' }],
                model: [{ required: true, message: '请输入型号', trigger: 'blur' }]
            };
        },

        async deleteComponent(row) {
            try {
                await this.$confirm(`确定要删除元器件"${row.model}"吗？`, '确认删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const response = await fetch(`/api/admin/components/${row.id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    this.$message.success('删除成功');
                    this.loadComponents();
                } else {
                    this.$message.error(result.error || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除请求失败');
                    console.error('Delete component error:', error);
                }
            }
        },

        // ==================== 对话框相关方法 ====================

        // 编辑对话框中制造商变化
        onEditManufacturerChange() {
            this.editForm.product_group_id = '';
            this.editForm.sub_product_group_id = '';
        },

        onEditComponentManufacturerChange() {
            this.editForm.product_group_id = '';
            this.editForm.sub_product_group_id = '';
        },

        onEditComponentProductGroupChange() {
            this.editForm.sub_product_group_id = '';
        },

        // 添加对话框中制造商变化
        onAddManufacturerChange() {
            this.addForm.product_group_id = '';
            this.addForm.sub_product_group_id = '';
        },

        onAddComponentManufacturerChange() {
            this.addForm.product_group_id = '';
            this.addForm.sub_product_group_id = '';
        },

        onAddComponentProductGroupChange() {
            this.addForm.sub_product_group_id = '';
        },

        // 提交编辑
        async submitEdit() {
            try {
                await this.$refs.editForm.validate();

                let url = '';
                let method = 'PUT';

                switch (this.editDialogType) {
                    case 'manufacturer':
                        url = `/api/admin/manufacturers/${this.editForm.id}`;
                        break;
                    case 'product_group':
                        url = `/api/admin/product_groups/${this.editForm.id}`;
                        break;
                    case 'sub_product_group':
                        url = `/api/admin/sub_product_groups/${this.editForm.id}`;
                        break;
                    case 'component':
                        url = `/api/admin/components/${this.editForm.id}`;
                        break;
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.editForm)
                });

                const result = await response.json();

                if (result.success) {
                    this.$message.success('更新成功');
                    this.editDialogVisible = false;

                    // 重新加载对应的数据
                    switch (this.editDialogType) {
                        case 'manufacturer':
                            this.loadManufacturers();
                            this.loadAllData();
                            break;
                        case 'product_group':
                            this.loadProductGroups();
                            this.loadAllData();
                            break;
                        case 'sub_product_group':
                            this.loadSubProductGroups();
                            this.loadAllData();
                            break;
                        case 'component':
                            this.loadComponents();
                            break;
                    }
                } else {
                    this.$message.error(result.error || '更新失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('更新请求失败');
                    console.error('Submit edit error:', error);
                }
            }
        },

        // 提交添加
        async submitAdd() {
            try {
                await this.$refs.addForm.validate();

                let url = '';
                let method = 'POST';

                switch (this.addDialogType) {
                    case 'manufacturer':
                        url = '/api/manufacturers';
                        break;
                    case 'product_group':
                        url = '/api/product_groups';
                        break;
                    case 'sub_product_group':
                        url = '/api/sub_product_groups';
                        break;
                    case 'component':
                        url = '/api/components';
                        break;
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.addForm)
                });

                const result = await response.json();

                if (result.id || result.success !== false) {
                    this.$message.success('添加成功');
                    this.addDialogVisible = false;

                    // 重新加载对应的数据
                    switch (this.addDialogType) {
                        case 'manufacturer':
                            this.loadManufacturers();
                            this.loadAllData();
                            break;
                        case 'product_group':
                            this.loadProductGroups();
                            this.loadAllData();
                            break;
                        case 'sub_product_group':
                            this.loadSubProductGroups();
                            this.loadAllData();
                            break;
                        case 'component':
                            this.loadComponents();
                            break;
                    }
                } else {
                    this.$message.error(result.error || '添加失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('添加请求失败');
                    console.error('Submit add error:', error);
                }
            }
        },

        // ==================== 批量删除功能 ====================

        // 制造商批量选择
        handleManufacturerSelectionChange(selection) {
            this.selectedManufacturers = selection;
        },

        // 产品组批量选择
        handleProductGroupSelectionChange(selection) {
            this.selectedProductGroups = selection;
        },

        // 子产品组批量选择
        handleSubProductGroupSelectionChange(selection) {
            this.selectedSubProductGroups = selection;
        },

        // 元器件批量选择
        handleComponentSelectionChange(selection) {
            this.selectedComponents = selection;
        },

        // 批量删除制造商
        async batchDeleteManufacturers() {
            if (this.selectedManufacturers.length === 0) {
                this.$message.warning('请选择要删除的制造商');
                return;
            }

            try {
                await this.$confirm(`确定要删除选中的 ${this.selectedManufacturers.length} 个制造商吗？`, '确认批量删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const deletePromises = this.selectedManufacturers.map(manufacturer =>
                    fetch(`/api/admin/manufacturers/${manufacturer.id}`, { method: 'DELETE' })
                        .then(response => response.json())
                        .then(result => ({ id: manufacturer.id, name: manufacturer.name, result }))
                );

                const results = await Promise.all(deletePromises);

                let successCount = 0;
                let failedItems = [];

                results.forEach(({ id, name, result }) => {
                    if (result.success) {
                        successCount++;
                    } else {
                        failedItems.push(`${name}: ${result.error || '删除失败'}`);
                    }
                });

                if (successCount > 0) {
                    this.$message.success(`成功删除 ${successCount} 个制造商`);
                    this.loadManufacturers();
                    this.loadAllData();
                }

                if (failedItems.length > 0) {
                    this.$message.error(`删除失败的项目：\n${failedItems.join('\n')}`);
                }

                this.selectedManufacturers = [];

            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量删除请求失败');
                    console.error('Batch delete manufacturers error:', error);
                }
            }
        },

        // 批量删除产品组
        async batchDeleteProductGroups() {
            if (this.selectedProductGroups.length === 0) {
                this.$message.warning('请选择要删除的产品组');
                return;
            }

            try {
                await this.$confirm(`确定要删除选中的 ${this.selectedProductGroups.length} 个产品组吗？`, '确认批量删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const deletePromises = this.selectedProductGroups.map(productGroup =>
                    fetch(`/api/admin/product_groups/${productGroup.id}`, { method: 'DELETE' })
                        .then(response => response.json())
                        .then(result => ({ id: productGroup.id, name: productGroup.name, result }))
                );

                const results = await Promise.all(deletePromises);

                let successCount = 0;
                let failedItems = [];

                results.forEach(({ id, name, result }) => {
                    if (result.success) {
                        successCount++;
                    } else {
                        failedItems.push(`${name}: ${result.error || '删除失败'}`);
                    }
                });

                if (successCount > 0) {
                    this.$message.success(`成功删除 ${successCount} 个产品组`);
                    this.loadProductGroups();
                    this.loadAllData();
                }

                if (failedItems.length > 0) {
                    this.$message.error(`删除失败的项目：\n${failedItems.join('\n')}`);
                }

                this.selectedProductGroups = [];

            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量删除请求失败');
                    console.error('Batch delete product groups error:', error);
                }
            }
        },

        // 批量删除子产品组
        async batchDeleteSubProductGroups() {
            if (this.selectedSubProductGroups.length === 0) {
                this.$message.warning('请选择要删除的子产品组');
                return;
            }

            try {
                await this.$confirm(`确定要删除选中的 ${this.selectedSubProductGroups.length} 个子产品组吗？`, '确认批量删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const deletePromises = this.selectedSubProductGroups.map(subProductGroup =>
                    fetch(`/api/admin/sub_product_groups/${subProductGroup.id}`, { method: 'DELETE' })
                        .then(response => response.json())
                        .then(result => ({ id: subProductGroup.id, name: subProductGroup.name, result }))
                );

                const results = await Promise.all(deletePromises);

                let successCount = 0;
                let failedItems = [];

                results.forEach(({ id, name, result }) => {
                    if (result.success) {
                        successCount++;
                    } else {
                        failedItems.push(`${name}: ${result.error || '删除失败'}`);
                    }
                });

                if (successCount > 0) {
                    this.$message.success(`成功删除 ${successCount} 个子产品组`);
                    this.loadSubProductGroups();
                    this.loadAllData();
                }

                if (failedItems.length > 0) {
                    this.$message.error(`删除失败的项目：\n${failedItems.join('\n')}`);
                }

                this.selectedSubProductGroups = [];

            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量删除请求失败');
                    console.error('Batch delete sub product groups error:', error);
                }
            }
        },

        // 批量删除元器件
        async batchDeleteComponents() {
            if (this.selectedComponents.length === 0) {
                this.$message.warning('请选择要删除的元器件');
                return;
            }

            try {
                await this.$confirm(`确定要删除选中的 ${this.selectedComponents.length} 个元器件吗？`, '确认批量删除', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const deletePromises = this.selectedComponents.map(component =>
                    fetch(`/api/admin/components/${component.id}`, { method: 'DELETE' })
                        .then(response => response.json())
                        .then(result => ({ id: component.id, model: component.model, result }))
                );

                const results = await Promise.all(deletePromises);

                let successCount = 0;
                let failedItems = [];

                results.forEach(({ id, model, result }) => {
                    if (result.success) {
                        successCount++;
                    } else {
                        failedItems.push(`${model}: ${result.error || '删除失败'}`);
                    }
                });

                if (successCount > 0) {
                    this.$message.success(`成功删除 ${successCount} 个元器件`);
                    this.loadComponents();
                }

                if (failedItems.length > 0) {
                    this.$message.error(`删除失败的项目：\n${failedItems.join('\n')}`);
                }

                this.selectedComponents = [];

            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量删除请求失败');
                    console.error('Batch delete components error:', error);
                }
            }
        }
    }
});
