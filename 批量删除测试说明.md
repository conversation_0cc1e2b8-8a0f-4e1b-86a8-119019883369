# 批量删除功能测试说明

## 测试环境准备

1. 启动服务器：`python run.py`
2. 访问管理页面：`http://localhost:5000/admin`
3. 使用管理密码登录：`147258369`

## 测试步骤

### 1. 制造商管理批量删除测试

#### 准备测试数据
1. 进入"制造商管理"标签页
2. 添加几个测试制造商（如：TestMfg1, TestMfg2, TestMfg3）
3. 确保有些制造商没有关联的产品组，有些有关联的产品组

#### 测试场景
1. **全部成功删除**：
   - 选择没有关联产品组的制造商
   - 点击"批量删除"按钮
   - 确认删除
   - 验证：显示成功删除数量，表格数据刷新

2. **部分失败删除**：
   - 选择混合的制造商（有些有关联，有些没有）
   - 点击"批量删除"按钮
   - 确认删除
   - 验证：显示成功数量和失败项目详情

3. **全部失败删除**：
   - 选择都有关联产品组的制造商
   - 点击"批量删除"按钮
   - 确认删除
   - 验证：显示所有失败项目和原因

### 2. 产品组管理批量删除测试

#### 准备测试数据
1. 进入"产品组管理"标签页
2. 添加几个测试产品组
3. 确保有些产品组没有关联的子产品组，有些有关联

#### 测试场景
- 重复制造商管理的测试场景
- 验证关联子产品组的检查逻辑

### 3. 子产品组管理批量删除测试

#### 准备测试数据
1. 进入"子产品组管理"标签页
2. 添加几个测试子产品组
3. 确保有些子产品组没有关联的元器件，有些有关联

#### 测试场景
- 重复前面的测试场景
- 验证关联元器件的检查逻辑

### 4. 元器件管理批量删除测试

#### 准备测试数据
1. 进入"元器件管理"标签页
2. 添加几个测试元器件

#### 测试场景
1. **批量删除元器件**：
   - 选择多个元器件
   - 点击"批量删除"按钮
   - 确认删除
   - 验证：所有元器件都应该成功删除（因为没有下级关联）

## 界面功能测试

### 1. 复选框功能
- **单选**：点击单个复选框，验证选择状态
- **全选**：点击表头复选框，验证全选/取消全选
- **混合选择**：部分选择后，验证表头复选框的中间状态

### 2. 批量删除按钮状态
- **禁用状态**：未选择任何项目时，按钮应该禁用
- **启用状态**：选择项目后，按钮应该启用
- **数量显示**：按钮文本应该显示正确的选择数量

### 3. 确认对话框
- **显示内容**：对话框应该显示正确的删除数量
- **取消操作**：点击取消应该不执行删除
- **确认操作**：点击确定应该执行删除

## 错误处理测试

### 1. 网络错误模拟
- 断开网络连接
- 执行批量删除操作
- 验证错误提示

### 2. 服务器错误模拟
- 停止服务器
- 执行批量删除操作
- 验证错误提示

## 性能测试

### 1. 大量数据删除
- 创建50-100个测试数据
- 执行批量删除
- 观察响应时间和内存使用

### 2. 并发删除
- 同时选择大量项目
- 验证并行删除的效果

## 预期结果

### 1. 成功场景
```
成功删除 3 个制造商
```

### 2. 部分失败场景
```
成功删除 2 个制造商
删除失败的项目：
Intel: 该制造商下还有产品组，无法删除
```

### 3. 完全失败场景
```
删除失败的项目：
Intel: 该制造商下还有产品组，无法删除
AMD: 该制造商下还有产品组，无法删除
NVIDIA: 该制造商下还有产品组，无法删除
```

## 测试检查清单

- [ ] 制造商批量删除功能正常
- [ ] 产品组批量删除功能正常
- [ ] 子产品组批量删除功能正常
- [ ] 元器件批量删除功能正常
- [ ] 复选框选择功能正常
- [ ] 批量删除按钮状态正确
- [ ] 确认对话框显示正确
- [ ] 成功删除提示正确
- [ ] 失败删除提示详细
- [ ] 数据完整性检查有效
- [ ] 页面刷新和选择清空正常
- [ ] 错误处理机制完善

## 注意事项

1. **测试数据**：使用测试数据，避免删除重要数据
2. **数据备份**：测试前备份数据库文件
3. **浏览器控制台**：关注JavaScript错误信息
4. **网络监控**：观察API请求和响应
5. **用户体验**：注意操作流程的流畅性
