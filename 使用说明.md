# 电子元器件管理系统使用说明

## 系统概述

这是一个基于Web的电子元器件资料管理系统，支持本地运行，无需外网连接。系统采用三级分类管理（制造商 → 产品组 → 子产品组），提供完整的元器件资料录入、搜索、浏览和导出功能。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
python backend/init_db.py
```

### 3. 启动系统
```bash
python run.py
```

系统启动后会自动打开浏览器访问 http://localhost:5000

## 功能说明

### 数据录入

#### 单条录入
1. 点击"数据录入"标签页
2. 依次选择制造商、产品组、子产品组
3. 填写元器件型号（必填）、描述、备注
4. 点击"添加元器件"按钮

#### 批量录入
1. 在数据录入页面点击"批量录入"按钮
2. 选择统一的制造商、产品组、子产品组
3. 在表格中填写多个元器件的型号、描述、备注
4. 点击"添加行"可增加更多录入行
5. 点击"批量提交"完成录入

### 数据导出

新版本将搜索导出和分类浏览功能整合到一个统一的"数据导出"页面中，界面更加整洁专业。

#### 使用步骤

1. 点击"数据导出"标签页
2. 在左侧选择区域，可以通过两种方式选择元器件：

**方式一：关键词搜索**
- 选择"关键词搜索"模式
- 在搜索框输入型号、描述或备注关键词
- 按回车或点击搜索按钮
- 在搜索结果中勾选需要的元器件，或点击"全选"
- 选中的元器件会自动添加到右侧已选择列表

**方式二：分类浏览**
- 选择"分类浏览"模式
- 在左侧分类树中点击子产品组节点
- 右侧会显示该分类下的所有元器件
- 勾选需要的元器件，或点击"全选"
- 选中的元器件会自动添加到右侧已选择列表

#### 混合使用

两种选择方式可以混合使用：
- 可以先通过搜索选择一些元器件
- 再切换到分类浏览选择其他元器件
- 所有选择的元器件会累加在右侧已选择列表中
- 可以在已选择列表中单独移除不需要的元器件
- 最后点击"导出Excel"按钮统一导出

#### 界面特点

- **统一界面**：搜索和分类浏览功能整合在同一页面
- **模式切换**：通过顶部按钮快速切换搜索和分类模式
- **实时预览**：右侧实时显示已选择的元器件数量
- **批量操作**：支持全选和批量移除功能
- **专业设计**：现代化的界面设计，操作更加直观

## 数据管理

### 分类管理
系统预置了一些示例分类数据：
- 制造商：TI、ST、Infineon、NXP、Analog Devices
- 产品组：模拟IC、数字IC、微控制器、传感器、功率器件
- 子产品组：运算放大器、比较器、逻辑门、8位MCU、32位MCU

如需添加新的分类，可以通过API接口或直接操作数据库。

### 数据库位置
- 数据库文件：`data/components.db`
- 导出文件：`exports/` 目录

### 备份建议
定期备份 `data/components.db` 文件以防数据丢失。

## 导出格式

导出的Excel文件包含以下列：
1. 制造商(1级分类)
2. 产品组(2级分类)  
3. 子产品(3级分类)
4. 型号
5. 描述
6. 备注

文件名格式：`元器件清单_YYYYMMDD_HHMMSS.xlsx`

## 技术特点

- **本地运行**：无需外网连接，数据完全本地存储
- **Web界面**：现代化的响应式Web界面，支持桌面和移动设备
- **SQLite数据库**：轻量级数据库，无需额外配置
- **实时搜索**：支持型号、描述、备注的模糊搜索
- **批量操作**：支持批量录入和批量导出
- **数据安全**：支持特殊字符，防止数据丢失

## 故障排除

### 启动失败
1. 检查Python版本（建议3.7+）
2. 确认已安装所有依赖：`pip install -r requirements.txt`
3. 检查端口5000是否被占用

### 数据库错误
1. 确认data目录存在
2. 重新运行：`python backend/init_db.py`

### 浏览器无法访问
1. 检查防火墙设置
2. 尝试使用 http://127.0.0.1:5000

## 联系支持

如遇到问题，请检查：
1. 控制台错误信息
2. 浏览器开发者工具的错误信息
3. 数据库文件是否存在且可读写
