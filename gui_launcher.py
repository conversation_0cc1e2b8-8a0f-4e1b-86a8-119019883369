#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电气元器件管理系统 - GUI启动器
提供用户友好的图形界面来启动和停止Web服务
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import webbrowser
import time
import os
import sys
import subprocess
import socket
from datetime import datetime

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

class ComponentManagerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("电气元器件管理系统")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 设置窗口图标（如果有的话）
        try:
            # 可以添加图标文件
            pass
        except:
            pass
        
        # 服务器相关变量
        self.server_process = None
        self.server_thread = None
        self.is_server_running = False
        self.server_port = 5000
        
        # 创建界面
        self.create_widgets()
        
        # 检查端口是否被占用
        self.check_port_status()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(
            title_frame,
            text="电气元器件管理系统",
            font=("Arial", 16, "bold")
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            title_frame, 
            text="本地Web服务管理工具", 
            font=("Arial", 10)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(self.root, text="服务状态", padding=10)
        status_frame.pack(pady=20, padx=20, fill="x")
        
        self.status_label = ttk.Label(
            status_frame, 
            text="服务未启动", 
            font=("Arial", 12),
            foreground="red"
        )
        self.status_label.pack()
        
        self.url_label = ttk.Label(
            status_frame, 
            text="", 
            font=("Arial", 10),
            foreground="blue",
            cursor="hand2"
        )
        self.url_label.pack(pady=(5, 0))
        self.url_label.bind("<Button-1>", self.open_browser)
        
        # 控制按钮区域
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=20)
        
        self.start_button = ttk.Button(
            button_frame, 
            text="启动服务", 
            command=self.start_server,
            width=15
        )
        self.start_button.pack(side="left", padx=10)
        
        self.stop_button = ttk.Button(
            button_frame, 
            text="停止服务", 
            command=self.stop_server,
            width=15,
            state="disabled"
        )
        self.stop_button.pack(side="left", padx=10)
        
        self.browser_button = ttk.Button(
            button_frame, 
            text="打开浏览器", 
            command=self.open_browser,
            width=15,
            state="disabled"
        )
        self.browser_button.pack(side="left", padx=10)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="运行日志", padding=10)
        log_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill="both", expand=True)
        
        self.log_text = tk.Text(
            text_frame, 
            height=8, 
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 清空日志按钮
        clear_button = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_button.pack(pady=(10, 0))
        
        # 添加初始日志
        self.add_log("程序启动完成")
        
    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def check_port_status(self):
        """检查端口是否被占用"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            result = sock.connect_ex(('localhost', self.server_port))
            if result == 0:
                self.add_log(f"检测到端口 {self.server_port} 已被占用")
                self.status_label.config(text="端口被占用", foreground="orange")
            else:
                self.add_log(f"端口 {self.server_port} 可用")
        except Exception as e:
            self.add_log(f"端口检查失败: {e}")
        finally:
            sock.close()
            
    def start_server(self):
        """启动服务器"""
        if self.is_server_running:
            self.add_log("服务器已在运行中")
            return
            
        try:
            self.add_log("正在启动服务器...")
            
            # 创建必要的目录
            os.makedirs('data', exist_ok=True)
            os.makedirs('exports', exist_ok=True)
            
            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            
            # 更新界面状态
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.browser_button.config(state="normal")
            
            # 延迟打开浏览器
            self.root.after(2000, self._delayed_browser_open)
            
        except Exception as e:
            self.add_log(f"启动服务器失败: {e}")
            messagebox.showerror("错误", f"启动服务器失败: {e}")
            
    def _run_server(self):
        """在后台线程中运行服务器"""
        try:
            # 切换到backend目录以确保正确的导入路径
            original_cwd = os.getcwd()
            backend_path = os.path.join(os.path.dirname(__file__), 'backend')
            os.chdir(backend_path)

            from app import create_app

            app = create_app()
            self.is_server_running = True

            # 切换回原目录
            os.chdir(original_cwd)
            
            # 更新状态显示
            self.root.after(0, self._update_server_status, True)
            
            # 启动Flask应用（在后台运行，不显示控制台输出）
            app.run(host='localhost', port=self.server_port, debug=False, use_reloader=False)
            
        except Exception as e:
            self.is_server_running = False
            self.root.after(0, self.add_log, f"服务器运行错误: {e}")
            self.root.after(0, self._update_server_status, False)
            
    def _update_server_status(self, running):
        """更新服务器状态显示"""
        if running:
            self.status_label.config(text="服务运行中", foreground="green")
            self.url_label.config(text=f"http://localhost:{self.server_port}")
            self.add_log("服务器启动成功")
        else:
            self.status_label.config(text="服务已停止", foreground="red")
            self.url_label.config(text="")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.browser_button.config(state="disabled")
            
    def _delayed_browser_open(self):
        """延迟打开浏览器"""
        if self.is_server_running:
            self.open_browser()
            
    def stop_server(self):
        """停止服务器"""
        if not self.is_server_running:
            self.add_log("服务器未在运行")
            return
            
        try:
            self.add_log("正在停止服务器...")
            self.is_server_running = False
            
            # 更新界面状态
            self._update_server_status(False)
            self.add_log("服务器已停止")
            
        except Exception as e:
            self.add_log(f"停止服务器失败: {e}")
            
    def open_browser(self, event=None):
        """打开浏览器"""
        if self.is_server_running:
            try:
                # 强制使用HTTP协议，避免浏览器自动跳转到HTTPS
                url = f'http://localhost:{self.server_port}'
                self.add_log(f"正在打开: {url}")

                # 尝试多种方式打开浏览器
                try:
                    # 方法1: 直接使用webbrowser
                    webbrowser.open(url)
                except:
                    try:
                        # 方法2: 使用系统默认浏览器
                        import subprocess
                        subprocess.run(['start', url], shell=True, check=False)
                    except:
                        # 方法3: 使用cmd命令
                        os.system(f'start {url}')

                self.add_log("已打开浏览器")
            except Exception as e:
                self.add_log(f"打开浏览器失败: {e}")
        else:
            self.add_log("服务器未运行，无法打开浏览器")
            
    def on_closing(self):
        """窗口关闭事件处理"""
        if self.is_server_running:
            result = messagebox.askyesno(
                "确认退出", 
                "服务器正在运行中，确定要退出吗？\n退出后服务器将停止运行。"
            )
            if result:
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
            
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ComponentManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")

if __name__ == '__main__':
    main()
