# 程序图标设置说明

## 当前状态
程序目前使用系统默认图标。如果需要自定义图标，请按以下步骤操作：

## 添加自定义图标

### 1. 准备图标文件
- 文件格式：`.ico` (Windows图标格式)
- 推荐尺寸：32x32, 48x48, 64x64, 128x128 像素
- 文件名：`app_icon.ico`
- 放置位置：项目根目录

### 2. 修改配置文件

#### 修改 `build_exe.py`
在 `create_pyinstaller_spec()` 函数中找到：
```python
icon=None,  # 可以添加图标文件路径
```

修改为：
```python
icon='app_icon.ico',  # 图标文件路径
```

#### 修改 `gui_launcher.py`
在 `__init__` 方法中找到：
```python
# 设置窗口图标（如果有的话）
try:
    # 可以添加图标文件
    pass
except:
    pass
```

修改为：
```python
# 设置窗口图标
try:
    self.root.iconbitmap('app_icon.ico')
except:
    pass  # 如果图标文件不存在，使用默认图标
```

### 3. 重新打包
运行 `build.bat` 或 `python build_exe.py` 重新打包程序。

## 图标设计建议

### 设计元素
- 电子元器件相关图标（如芯片、电路板、电阻等）
- 简洁明了的设计
- 在小尺寸下仍然清晰可见

### 颜色建议
- 主色调：蓝色或绿色（科技感）
- 辅助色：白色或灰色
- 避免过于复杂的颜色搭配

### 在线图标制作工具
- [Favicon.io](https://favicon.io/)
- [ICO Convert](https://icoconvert.com/)
- [Online ICO Converter](https://www.online-convert.com/result#j=f8b8c5e1-7c5a-4c5e-9b8a-1c5e7b8c5e1c)

## 示例图标创建流程

1. **设计图标**
   - 使用设计软件创建 PNG 格式图标
   - 尺寸：256x256 像素
   - 背景透明

2. **转换格式**
   - 使用在线工具将 PNG 转换为 ICO
   - 生成多种尺寸的图标

3. **测试图标**
   - 将 `app_icon.ico` 放在项目根目录
   - 修改配置文件
   - 重新打包测试

## 注意事项

- 图标文件必须是 `.ico` 格式
- 图标文件路径要正确
- 如果图标文件不存在，程序会使用默认图标
- 重新打包后图标才会生效

## 当前默认行为

由于没有自定义图标，程序会：
- GUI窗口使用系统默认图标
- 可执行文件使用 Python 默认图标
- 任务栏显示默认图标

这不影响程序功能，只是视觉效果的差异。
