/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.main-container {
    height: 100vh;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: 0 4px 20px 0 rgba(79, 70, 229, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 500;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-link {
    color: white !important;
    font-size: 14px;
    text-decoration: none;
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.admin-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.nav-menu {
    background: transparent !important;
    border: none !important;
}

.nav-menu .el-menu-item {
    color: white !important;
    border-bottom: 2px solid transparent !important;
    font-size: 16px;
    font-weight: 500;
}

.nav-menu .el-menu-item:hover,
.nav-menu .el-menu-item.is-active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-bottom-color: white !important;
    color: white !important;
}

/* 主体内容样式 */
.main-content {
    padding: 24px;
    background: transparent;
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.el-card {
    border-radius: 16px;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(229, 231, 235, 0.8);
    margin-bottom: 24px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.el-card:hover {
    box-shadow: 0 8px 32px 0 rgba(79, 70, 229, 0.12);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 16px;
}

.el-card__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    border-radius: 16px 16px 0 0;
    padding: 20px 24px;
}

/* 表单样式 */
.entry-form .el-form-item {
    margin-bottom: 20px;
}

.entry-form .el-select,
.entry-form .el-input {
    width: 100%;
}

.batch-form .el-form-item {
    margin-bottom: 15px;
}

.batch-form .el-select {
    width: 100%;
}

/* 批量分类设置头部 */
.batch-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 8px;
}

.batch-category-title {
    font-size: 14px;
    font-weight: 600;
    color: #0369a1;
}

.batch-category-header .el-button {
    border-radius: 6px;
    font-weight: 600;
    padding: 8px 16px;
    font-size: 13px;
}

/* 表格样式 */
.el-table {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.el-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.el-table td, .el-table th {
    border-bottom: 1px solid #f3f4f6;
    padding: 12px 16px;
}

.el-table tbody tr:hover > td {
    background-color: rgba(79, 70, 229, 0.04) !important;
}

/* 导出页面样式 */
.export-left-card {
    height: calc(100vh - 140px);
}

.export-right-card {
    height: calc(100vh - 140px);
}

.export-mode-content {
    height: calc(100vh - 220px);
    display: flex;
    flex-direction: column;
}

.search-section {
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
}

.results-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 5px;
}

.results-count {
    font-weight: 500;
    color: #606266;
}

.category-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.category-components-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.section-title {
    font-weight: 500;
    color: #606266;
    margin-bottom: 10px;
    padding: 0 5px;
}

.selected-list {
    flex: 1;
    margin-bottom: 20px;
}

.export-actions {
    padding: 10px 0;
}

/* 分类树样式 */
.category-tree-container {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    max-height: 650px;
    min-height: 500px;
}

.category-tree {
    padding: 16px;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    margin: 2px 0;
}

.custom-tree-node:hover {
    background-color: rgba(79, 70, 229, 0.08);
    transform: translateX(2px);
}

.tree-node-count {
    color: #6b7280;
    font-size: 11px;
    font-weight: 600;
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 24px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.el-button {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-button--primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: none;
    color: white;
}

.el-button--primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.el-button--success {
    background: linear-gradient(135deg, #42b883 0%, #369870 100%);
    border: none;
}

.el-button--success:hover {
    background: linear-gradient(135deg, #369870 0%, #2d7d5d 100%);
}

/* 输入框样式 */
.el-input__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.el-input__inner:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 选择器样式 */
.el-select .el-input__inner {
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        height: auto;
        padding: 15px;
    }
    
    .header h1 {
        margin-bottom: 10px;
        font-size: 20px;
    }
    
    .main-content {
        padding: 10px;
    }
    
    .el-col {
        margin-bottom: 20px;
    }
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 消息提示样式 */
.el-message {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式增强 */
.el-button {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.el-button--success {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
}

.el-button--success:hover {
    background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.el-radio-button__inner {
    border-radius: 4px;
    font-weight: 500;
}

/* 表格样式增强 */
.el-table {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.el-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.el-table tr:hover > td {
    background-color: #f8f9fa !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #c1c1c1 0%, #a8a8a8 100%);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #a8a8a8 0%, #909090 100%);
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 分类树节点样式增强 */
.el-tree-node__content {
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.2s ease;
    padding: 8px 12px !important;
}

.el-tree-node__content:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(124, 58, 237, 0.06) 100%) !important;
    transform: translateX(4px);
}

.el-tree-node.is-current > .el-tree-node__content {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15) 0%, rgba(124, 58, 237, 0.12) 100%) !important;
    color: #4f46e5 !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.2);
}

.el-tree-node__expand-icon {
    color: #6b7280;
    font-size: 14px;
    transition: all 0.2s ease;
}

.el-tree-node__expand-icon:hover {
    color: #4f46e5;
    transform: scale(1.1);
}

/* 二级分类展开箭头缩进 - 通过层级深度计算 */
.el-tree-node .el-tree-node .el-tree-node__expand-icon {
    margin-left: 16px;
}

/* 三级分类展开箭头缩进 */
.el-tree-node .el-tree-node .el-tree-node .el-tree-node__expand-icon {
    margin-left: 32px;
}

/* 分类树层级样式优化 - 简洁版 */
.custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 6px 8px;
    transition: all 0.2s ease;
    margin: 1px 0;
}

/* 一级分类（制造商）- 稍大字体，加粗 */
.custom-tree-node.level-1 {
    font-size: 15px;
    font-weight: 700;
    color: #1f2937;
    padding: 8px 8px;
    margin: 3px 0;
}

.custom-tree-node.level-1:hover {
    background: rgba(79, 70, 229, 0.05);
    color: #4f46e5;
}

.custom-tree-node.level-1 .tree-node-icon {
    font-size: 16px;
}

/* 二级分类（产品组）- 中等字体，半粗，右移 */
.custom-tree-node.level-2 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    padding: 6px 8px;
    margin: 2px 0;
}

.custom-tree-node.level-2:hover {
    background: rgba(79, 70, 229, 0.05);
    color: #4f46e5;
}

.custom-tree-node.level-2 .tree-node-icon {
    font-size: 14px;
}

/* 三级分类（子产品组）- 普通字体，更多右移 */
.custom-tree-node.level-3 {
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    padding: 5px 8px;
    margin: 1px 0 1px 32px;
    cursor: pointer;
}

.custom-tree-node.level-3:hover {
    background: rgba(79, 70, 229, 0.08);
    color: #4f46e5;
}

.custom-tree-node.level-3 .tree-node-icon {
    font-size: 13px;
}

/* 树节点内容容器 */
.tree-node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

/* 树节点图标样式 */
.tree-node-icon {
    display: inline-block;
    margin-right: 8px;
    opacity: 0.8;
    transition: all 0.2s ease;
}

.custom-tree-node:hover .tree-node-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* 树节点文本样式 */
.tree-node-text {
    transition: all 0.2s ease;
}

/* 元器件数量标签样式优化 - 简洁版 */
.tree-node-count {
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
    margin-left: 6px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.custom-tree-node:hover .tree-node-count {
    color: #4f46e5;
    font-weight: 600;
}

/* 覆盖Element UI默认样式 */
.el-tree-node__content {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.el-tree-node__content:hover {
    background: transparent !important;
    transform: none !important;
}

/* 搜索输入框样式增强 */
.search-input .el-input__inner {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.search-input .el-input__inner:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input .el-input-group__append {
    border-radius: 0 12px 12px 0;
    border: 2px solid #4f46e5;
    border-left: none;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.search-input .el-input-group__append .el-button {
    border-radius: 0;
    background: transparent;
    border: none;
    color: white;
}

/* 结果计数样式 */
.results-count {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #d1d5db;
}

/* 选择模式按钮组样式 */
.el-radio-group .el-radio-button__inner {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: 2px solid #e5e7eb;
}

.el-radio-group .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-color: #4f46e5;
    color: white;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

/* 导出区域样式增强 */
.export-left-card, .export-right-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 1px solid rgba(229, 231, 235, 0.8);
}

/* 表单输入框样式增强 */
.el-select .el-input__inner {
    border-radius: 8px;
    border: 2px solid #e5e7eb;
    transition: all 0.2s ease;
}

.el-select .el-input__inner:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 分类树容器样式 */
.category-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.category-components-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 已选择区域样式 */
.selected-list {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    padding: 12px;
    border: 1px solid #e2e8f0;
}

/* 导出按钮样式 */
.export-actions .el-button {
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 标题样式增强 */
.section-title {
    font-weight: 700;
    color: #1f2937;
    font-size: 16px;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 8px;
    border-left: 4px solid #4f46e5;
}

/* 搜索区域样式 */
.search-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
}

/* 结果区域样式 */
.results-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 批量录入表格样式 */
.batch-table {
    margin-top: 20px;
}

.batch-table .el-table__header th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 700;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.batch-table .el-input__count {
    font-size: 11px;
    color: #6b7280;
}

.batch-table .el-input.is-exceed .el-input__count {
    color: #f56c6c;
    font-weight: 600;
}

.batch-table .el-input__inner {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.batch-table .el-input__inner:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.batch-table .el-button--danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.batch-table .el-button--danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.batch-table .el-button--danger:disabled {
    background: #f3f4f6;
    color: #9ca3af;
    transform: none;
    box-shadow: none;
}

/* 批量录入错误对话框样式 */
.batch-error-dialog {
    max-width: 700px;
}

.batch-error-dialog .el-message-box__message {
    white-space: pre-line;
    max-height: 350px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    color: #374151;
}

.batch-error-dialog .el-message-box__header {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-bottom: 1px solid #fecaca;
    border-radius: 8px 8px 0 0;
}

.batch-error-dialog .el-message-box__title {
    color: #dc2626;
    font-weight: 700;
}

/* 批量录入操作按钮区域 */
.batch-actions {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    align-items: center;
}

.batch-actions .el-button {
    border-radius: 8px;
    font-weight: 600;
    padding: 10px 20px;
    transition: all 0.2s ease;
}

.batch-actions .el-button--primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.batch-actions .el-button--primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

/* 批量录入提示信息 */
.batch-info {
    margin-top: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    color: #1e40af;
    font-size: 13px;
    line-height: 1.4;
}

.batch-info .info-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.batch-info ul {
    margin: 8px 0 0 16px;
    padding: 0;
}

.batch-info li {
    margin-bottom: 2px;
}

/* 分页组件样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    margin-top: 12px;
    border-top: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
    border-radius: 0 0 12px 12px;
}

.el-pagination {
    padding: 0;
}

.el-pagination.is-background .el-pager li {
    background-color: #f4f4f5;
    color: #606266;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.el-pagination.is-background .el-pager li:hover {
    background-color: #e6e8eb;
    transform: translateY(-1px);
}

.el-pagination.is-background .el-pager li.active {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.el-pagination .btn-prev,
.el-pagination .btn-next {
    background-color: #f4f4f5;
    border-radius: 6px;
    margin: 0 4px;
    transition: all 0.2s ease;
}

.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
    background-color: #e6e8eb;
    transform: translateY(-1px);
}

.el-pagination .el-input__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    transition: all 0.2s ease;
}

.el-pagination .el-input__inner:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* 滚动条样式增强 */
.category-tree-container::-webkit-scrollbar,
.el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.category-tree-container::-webkit-scrollbar-track,
.el-table__body-wrapper::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.category-tree-container::-webkit-scrollbar-thumb,
.el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.category-tree-container::-webkit-scrollbar-thumb:hover,
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* 表格容器滚动条 */
.results-section .el-table,
.category-components-section .el-table,
.selected-list .el-table {
    border-radius: 8px;
    overflow: hidden;
}

.results-section .el-table__body-wrapper,
.category-components-section .el-table__body-wrapper,
.selected-list .el-table__body-wrapper {
    max-height: 350px;
    overflow-y: auto;
}

/* 分页容器在不同区域的样式调整 */
.results-section .pagination-container,
.category-components-section .pagination-container {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-top: 1px solid #e2e8f0;
}

.selected-list .pagination-container {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-top: 1px solid #d1d5db;
    padding: 12px 0;
}

/* 小尺寸分页样式 */
.el-pagination--small .el-pager li {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
}

.el-pagination--small .btn-prev,
.el-pagination--small .btn-next {
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
}

/* 响应式分页 */
@media (max-width: 768px) {
    .pagination-container {
        padding: 12px 0;
    }

    .el-pagination {
        text-align: center;
    }

    .el-pagination .el-pager {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Excel导入样式 */
.excel-upload-area {
    text-align: center;
    padding: 40px 20px;
}

.excel-upload-area .el-upload-dragger {
    width: 100%;
    height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 12px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.excel-upload-area .el-upload-dragger:hover {
    border-color: #4f46e5;
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.excel-upload-area .el-upload-dragger .el-icon-upload {
    font-size: 48px;
    color: #c0c4cc;
    margin: 40px 0 16px;
    line-height: 50px;
    transition: all 0.3s ease;
}

.excel-upload-area .el-upload-dragger:hover .el-icon-upload {
    color: #4f46e5;
    transform: scale(1.1);
}

.excel-upload-area .el-upload__text {
    color: #606266;
    font-size: 16px;
    text-align: center;
    margin-top: 20px;
    font-weight: 500;
}

.excel-upload-area .el-upload__text em {
    color: #4f46e5;
    font-style: normal;
    font-weight: 600;
}

.excel-upload-area .el-upload__tip {
    color: #909399;
    font-size: 13px;
    margin-top: 15px;
    line-height: 1.5;
}

.excel-upload-area .el-upload__tip p {
    margin: 4px 0;
}

.excel-data-area {
    padding: 20px 0;
}

.excel-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.excel-data-header span {
    font-weight: 700;
    color: #1f2937;
    font-size: 16px;
}

.excel-data-header .el-button {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.excel-data-header .el-button--primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.excel-data-header .el-button--primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.excel-pagination {
    margin-top: 20px;
    text-align: center;
    padding: 16px 0;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e5e7eb;
}

/* Excel表格样式 */
.excel-data-area .el-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.excel-data-area .el-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 700;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.excel-data-area .el-table td {
    border-bottom: 1px solid #f0f0f0;
    padding: 8px 0;
}

.excel-data-area .el-table tr:hover > td {
    background-color: #f8fafc !important;
}

.excel-data-area .el-input__inner {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
    font-size: 13px;
}

.excel-data-area .el-input__inner:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Excel导入对话框样式 */
.el-dialog__wrapper .el-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.el-dialog__header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 20px 24px;
    border-bottom: none;
}

.el-dialog__title {
    font-weight: 700;
    font-size: 18px;
}

.el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 20px;
    transition: all 0.2s ease;
}

.el-dialog__headerbtn .el-dialog__close:hover {
    color: #e0e7ff;
    transform: scale(1.1);
}

.el-dialog__body {
    padding: 24px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

/* 响应式Excel导入 */
@media (max-width: 768px) {
    .excel-data-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .excel-upload-area {
        padding: 20px 10px;
    }

    .excel-upload-area .el-upload-dragger {
        height: 150px;
    }

    .excel-upload-area .el-upload-dragger .el-icon-upload {
        font-size: 36px;
        margin: 20px 0 12px;
    }

    .excel-upload-area .el-upload__text {
        font-size: 14px;
    }

    .excel-data-area .el-table {
        font-size: 12px;
    }

    .excel-data-area .el-input__inner {
        font-size: 12px;
        padding: 6px 8px;
    }
}
