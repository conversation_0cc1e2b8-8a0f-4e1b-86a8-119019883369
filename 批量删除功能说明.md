# 批量删除功能说明

## 概述

为数据库管理页面的四个管理模块（制造商管理、产品组管理、子产品组管理、元器件管理）添加了批量删除功能。

## 功能特性

### 1. 复选框选择
- 每个表格都添加了复选框列，支持单选和全选
- 表格头部的复选框可以全选/取消全选当前页面的所有数据
- 每一行都有独立的复选框，可以单独选择

### 2. 批量删除按钮
- 每个管理模块都有独立的"批量删除"按钮
- 按钮显示当前选中的项目数量，如：`批量删除 (3)`
- 当没有选中任何项目时，按钮处于禁用状态
- 按钮颜色为红色（danger类型），提醒用户这是危险操作

### 3. 确认对话框
- 点击批量删除按钮时，会弹出确认对话框
- 对话框显示要删除的项目数量
- 用户可以选择"确定"或"取消"

### 4. 智能删除处理
- 批量删除会并行处理所有选中的项目
- 如果某些项目删除失败（如存在关联数据），会显示具体的失败原因
- 成功删除的项目会显示成功数量
- 删除完成后会自动刷新数据列表和清空选择

## 使用方法

### 制造商管理
1. 在制造商管理标签页中，勾选要删除的制造商
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"
4. 系统会检查每个制造商是否有关联的产品组，如有关联则删除失败

### 产品组管理
1. 在产品组管理标签页中，勾选要删除的产品组
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"
4. 系统会检查每个产品组是否有关联的子产品组，如有关联则删除失败

### 子产品组管理
1. 在子产品组管理标签页中，勾选要删除的子产品组
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"
4. 系统会检查每个子产品组是否有关联的元器件，如有关联则删除失败

### 元器件管理
1. 在元器件管理标签页中，勾选要删除的元器件
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"
4. 元器件没有下级关联，通常都能成功删除

## 界面布局

### 制造商管理和产品组管理
- 搜索框、添加按钮和批量删除按钮在同一行
- 布局比例：搜索区域较大，按钮区域适中

### 子产品组管理和元器件管理
- 由于筛选条件较多，批量删除按钮放在第二行
- 第一行：筛选条件和搜索框、添加按钮
- 第二行：批量删除按钮

## 安全特性

### 1. 数据完整性检查
- 删除制造商前检查是否有关联的产品组
- 删除产品组前检查是否有关联的子产品组
- 删除子产品组前检查是否有关联的元器件
- 有关联数据的项目会删除失败并显示错误信息

### 2. 用户确认
- 所有批量删除操作都需要用户确认
- 确认对话框明确显示要删除的项目数量

### 3. 错误处理
- 网络错误或服务器错误会显示相应的错误信息
- 部分删除失败时会详细列出失败的项目和原因

## 技术实现

### 前端
- 使用Element UI的表格复选框功能
- Vue.js响应式数据绑定
- Promise.all并行处理删除请求
- 详细的错误处理和用户反馈

### 后端
- 复用现有的单个删除API接口
- 保持原有的数据完整性检查逻辑
- 事务处理确保数据一致性

## 注意事项

1. **数据关系**：删除操作遵循数据层级关系，必须先删除下级数据
2. **性能考虑**：大量数据删除时建议分批进行
3. **备份建议**：重要数据删除前建议先备份数据库
4. **权限控制**：当前使用管理密码保护，生产环境建议加强权限控制

## 错误信息示例

### 成功删除
```
成功删除 3 个制造商
```

### 部分失败
```
成功删除 2 个制造商
删除失败的项目：
Intel: 该制造商下还有产品组，无法删除
```

### 完全失败
```
删除失败的项目：
Intel: 该制造商下还有产品组，无法删除
AMD: 该制造商下还有产品组，无法删除
NVIDIA: 该制造商下还有产品组，无法删除
```
