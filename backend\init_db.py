#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from models import Database

def main():
    """初始化数据库"""
    print("正在初始化数据库...")
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    # 创建数据库实例（会自动初始化表结构和示例数据）
    db = Database()
    
    print("数据库初始化完成！")
    print(f"数据库文件位置: {os.path.abspath(db.db_path)}")

if __name__ == '__main__':
    main()
